# Common dependencies
-r common.txt

# Dependencies for CPUs
torch==2.6.0+cpu; platform_machine == "x86_64"
torch==2.6.0; platform_system == "Darwin"
torch==2.6.0; platform_machine == "ppc64le" or platform_machine == "aarch64"
torch==2.7.0.dev20250304; platform_machine == "s390x"

# required for the image processor of minicpm-o-2_6, this must be updated alongside torch
torchaudio; platform_machine != "ppc64le" and platform_machine != "s390x"
torchaudio==2.6.0; platform_machine == "ppc64le"

# required for the image processor of phi3v, this must be updated alongside torch
torchvision; platform_machine != "ppc64le" and platform_machine != "s390x"
torchvision==0.21.0; platform_machine == "ppc64le"
datasets # for benchmark scripts

# cpu cannot use triton 3.3.0
triton==3.2.0; platform_machine == "x86_64"
