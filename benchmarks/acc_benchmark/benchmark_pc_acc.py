import time
import torch
from vllm import LLM, SamplingParams
from torch.profiler import profile, record_function, ProfilerActivity
# by triton
import triton
triton_version = triton.__version__

if triton_version.startswith("2.1"):    
    from triton.common.backend import compute_core_version_key
elif triton_version.startswith("3.0"):    
    from triton.compiler.compiler import triton_key

def trace_handler(p):
    output = p.key_averages().table(sort_by="self_cuda_time_total")
    print(output)
    p.export_chrome_trace("trace.json")


# A prompt containing a large markdown table. The table is randomly generated by GPT-4.
LONG_PROMPT = "You are a helpful assistant in recognizes the content of tables in markdown format. Here is a table as follows.\n# Table\n" + """
| ID  | Name          | Age | Occupation    | Country       | Email                  | Phone Number   | Address                       |
|-----|---------------|-----|---------------|---------------|------------------------|----------------|------------------------------|
| 1   | John Doe      | 29  | Engineer      | USA           | <EMAIL>   | 555-1234       | 123 Elm St, Springfield, IL  |
| 2   | Jane Smith    | 34  | Doctor        | Canada        | <EMAIL> | 555-5678       | 456 Oak St, Toronto, ON      |
| 3   | Alice Johnson | 27  | Teacher       | UK            | <EMAIL>    | 555-8765       | 789 Pine St, London, UK      |
| 4   | Bob Brown     | 45  | Artist        | Australia     | <EMAIL>      | 555-4321       | 321 Maple St, Sydney, NSW    |
| 5   | Carol White   | 31  | Scientist     | New Zealand   | <EMAIL>    | 555-6789       | 654 Birch St, Wellington, NZ |
| 6   | Dave Green    | 28  | Lawyer        | Ireland       | <EMAIL>     | 555-3456       | 987 Cedar St, Dublin, IE     |
| 7   | Emma Black    | 40  | Musician      | USA           | <EMAIL>     | 555-1111       | 246 Ash St, New York, NY     |
| 8   | Frank Blue    | 37  | Chef          | Canada        | <EMAIL>    | 555-2222       | 135 Spruce St, Vancouver, BC |
| 9   | Grace Yellow  | 50  | Engineer      | UK            | <EMAIL>    | 555-3333       | 864 Fir St, Manchester, UK   |
| 10  | Henry Violet  | 32  | Artist        | Australia     | <EMAIL>    | 555-4444       | 753 Willow St, Melbourne, VIC|
| 11  | Irene Orange  | 26  | Scientist     | New Zealand   | <EMAIL>    | 555-5555       | 912 Poplar St, Auckland, NZ  |
| 12  | Jack Indigo   | 38  | Teacher       | Ireland       | <EMAIL>     | 555-6666       | 159 Elm St, Cork, IE         |
| 13  | Karen Red     | 41  | Lawyer        | USA           | <EMAIL>    | 555-7777       | 357 Cedar St, Boston, MA     |
| 14  | Leo Brown     | 30  | Chef          | Canada        | <EMAIL>      | 555-8888       | 246 Oak St, Calgary, AB      |
| 15  | Mia Green     | 33  | Musician      | UK            | <EMAIL>      | 555-9999       | 975 Pine St, Edinburgh, UK   |
| 16  | Noah Yellow   | 29  | Doctor        | Australia     | <EMAIL>     | 555-0000       | 864 Birch St, Brisbane, QLD  |
| 17  | Olivia Blue   | 35  | Engineer      | New Zealand   | <EMAIL>   | 555-1212       | 753 Maple St, Hamilton, NZ   |
| 18  | Peter Black   | 42  | Artist        | Ireland       | <EMAIL>    | 555-3434       | 912 Fir St, Limerick, IE     |
| 19  | Quinn White   | 28  | Scientist     | USA           | <EMAIL>    | 555-5656       | 159 Willow St, Seattle, WA   |
| 20  | Rachel Red    | 31  | Teacher       | Canada        | <EMAIL>   | 555-7878       | 357 Poplar St, Ottawa, ON    |
| 21  | Steve Green   | 44  | Lawyer        | UK            | <EMAIL>    | 555-9090       | 753 Elm St, Birmingham, UK   |
| 22  | Tina Blue     | 36  | Musician      | Australia     | <EMAIL>     | 555-1213       | 864 Cedar St, Perth, WA      |
| 23  | Umar Black    | 39  | Chef          | New Zealand   | <EMAIL>     | 555-3435       | 975 Spruce St, Christchurch, NZ|
| 24  | Victor Yellow | 43  | Engineer      | Ireland       | <EMAIL>   | 555-5657       | 246 Willow St, Galway, IE    |
| 25  | Wendy Orange  | 27  | Artist        | USA           | <EMAIL>    | 555-7879       | 135 Elm St, Denver, CO       |
| 26  | Xavier Green  | 34  | Scientist     | Canada        | <EMAIL>   | 555-9091       | 357 Oak St, Montreal, QC     |
| 27  | Yara Red      | 41  | Teacher       | UK            | <EMAIL>     | 555-1214       | 975 Pine St, Leeds, UK       |
| 28  | Zack Blue     | 30  | Lawyer        | Australia     | <EMAIL>     | 555-3436       | 135 Birch St, Adelaide, SA   |
| 29  | Amy White     | 33  | Musician      | New Zealand   | <EMAIL>      | 555-5658       | 159 Maple St, Wellington, NZ |
| 30  | Ben Black     | 38  | Chef          | Ireland       | <EMAIL>      | 555-7870       | 246 Fir St, Waterford, IE    |
"""


def get_generation_time(llm, sampling_params, prompts, idx=0):
    # time the generation
    #start_time = time.time()
    #print("=======prompts===:",prompts)
    #with profile(
    #    activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
    #    record_shapes=True,
    #    profile_memory=True,
    #    with_stack=True,
    #    schedule=torch.profiler.schedule(
    #        wait=0,
    #        warmup=0,
    #        active=1,
    #        repeat=1
    #        ),
    #    on_trace_ready=trace_handler
    #    ) as p:
    
    # by triton
    if triton_version.startswith("2.1"):        
        version_key = compute_core_version_key()    
    if triton_version.startswith("3.0"):        
        version_key = triton_key()
    
    start_time = time.time()
    output = llm.generate(prompts, sampling_params=sampling_params)
    end_time = time.time()
    #print(p.key_averages().table(sort_by="self_cuda_time_total"))
    #p.export_chrome_trace(f"trace_{idx}.json")
    # print the output and generation time
    #print("----output---:",output)
    print(f"Output: {output[0].outputs[0].text}")
    print(f"Generation time: {end_time - start_time} seconds.")


if __name__ == '__main__':
    # set enable_prefix_caching=True to enable APC
    llm = LLM(
        model='/mnt/data/llm-models/qwen3/Qwen3-30B-A3B/',
        dtype='float16',
        #trust_remote_code=True,
        #enforce_eager=True,
        enable_prefix_caching=True,
        tensor_parallel_size=2,
        #guided_decoding_backend="lm-format-enforcer"
    )

    sampling_params = SamplingParams(temperature=0, max_tokens=100)

    # 29 30 33 40
    name_list = ["John Doe", "Zack Blue", "Amy White", "Emma Black"]

    for idx, name in enumerate(name_list):
        get_generation_time(
            llm,
            sampling_params,
            LONG_PROMPT + f"Question: what is the age of {name}? Your answer: The age of {name} is",
            idx
        )