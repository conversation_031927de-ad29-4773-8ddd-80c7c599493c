#!/usr/bin/env python3
# SPDX-License-Identifier: Apache-2.0
"""
测试reset_prefix_cache功能的简单脚本

使用方法:
1. 启动vLLM服务时设置 VLLM_SERVER_DEV_MODE=1
2. 运行此脚本验证reset功能是否正常工作
"""

import asyncio
import argparse
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend_request_func import reset_prefix_cache


async def test_reset_cache(base_url: str):
    """测试reset_prefix_cache功能"""
    
    print(f"测试服务器: {base_url}")
    print("="*50)
    
    # 测试reset_prefix_cache API
    print("1. 测试reset_prefix_cache API...")
    success = await reset_prefix_cache(base_url)
    
    if success:
        print("✓ reset_prefix_cache API调用成功!")
        print("✓ vLLM服务支持prefix cache重置功能")
        return True
    else:
        print("✗ reset_prefix_cache API调用失败!")
        print("\n可能的原因:")
        print("1. vLLM服务未启用dev模式 (需要设置 VLLM_SERVER_DEV_MODE=1)")
        print("2. 服务器URL不正确")
        print("3. vLLM服务未运行或无法访问")
        print("4. vLLM版本不支持此功能")
        return False


async def main():
    parser = argparse.ArgumentParser(description="测试reset_prefix_cache功能")
    parser.add_argument("--base-url", type=str, help="服务器基础URL")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="服务器主机")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    
    args = parser.parse_args()
    
    # 构建base_url
    if args.base_url:
        base_url = args.base_url
    else:
        base_url = f"http://{args.host}:{args.port}"
    
    print("vLLM Prefix Cache Reset 功能测试")
    print("="*50)
    
    success = await test_reset_cache(base_url)
    
    print("\n" + "="*50)
    if success:
        print("✓ 测试通过! 可以使用批量测试脚本")
        print("\n下一步:")
        print("python batch_test_hit_rates.py --model your_model --hit-rates 0.0 0.5 1.0")
    else:
        print("✗ 测试失败! 请检查vLLM服务配置")
        print("\n启动vLLM服务的正确方式:")
        print("VLLM_SERVER_DEV_MODE=1 python -m vllm.entrypoints.openai.api_server \\")
        print("    --model your_model --enable-prefix-caching --port 8000")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
