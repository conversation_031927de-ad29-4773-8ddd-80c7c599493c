import asyncio
import aiohttp
import json
import random
import time
import argparse
import sys

class BenchmarkConfig:
    def __init__(self, args=None):
        """
        初始化基准测试配置

        Args:
            args: 命令行参数对象，如果为None则使用默认值
        """
        if args:
            self.concurrent_requests = args.concurrent_requests
            self.total_requests = args.total_requests
            self.input_tokens_min = args.input_tokens_min
            self.input_tokens_max = args.input_tokens_max
            self.output_tokens_min = args.output_tokens_min
            self.output_tokens_max = args.output_tokens_max
            self.port = args.port
            self.model = args.model
        else:
            # 默认值（保持向后兼容性）
            self.concurrent_requests = 64  # 并发数
            self.total_requests = 64     # 总请求数
            self.input_tokens_min = 800    # 输入最小token数
            self.input_tokens_max = 1000   # 输入最大token数
            self.output_tokens_min = 300   # 输出最小token数
            self.output_tokens_max = 500  # 输出最大token数
            self.port = 8062  # 默认端口号
            self.model = "qwen"

        # 构建完整URL
        self.url = f"http://localhost:{self.port}/v1/chat/completions"

        # 参数验证
        self._validate_config()

    def _validate_config(self):
        """验证配置参数的有效性"""
        if self.concurrent_requests <= 0:
            raise ValueError("并发请求数必须大于0")

        if self.total_requests <= 0:
            raise ValueError("总请求数必须大于0")

        if self.input_tokens_min <= 0 or self.input_tokens_max <= 0:
            raise ValueError("输入token数必须大于0")

        if self.input_tokens_min >= self.input_tokens_max:
            raise ValueError("输入token最小值必须小于最大值")

        if self.output_tokens_min <= 0 or self.output_tokens_max <= 0:
            raise ValueError("输出token数必须大于0")

        if self.output_tokens_min >= self.output_tokens_max:
            raise ValueError("输出token最小值必须小于最大值")

        if not (1 <= self.port <= 65535):
            raise ValueError("端口号必须在1-65535范围内")

        if not self.model.strip():
            raise ValueError("模型名称不能为空")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="vLLM 基准测试工具 - 测试API服务的并发性能和准确性",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用默认参数运行
  python benchmark_acc.py

  # 自定义并发数和总请求数
  python benchmark_acc.py --concurrent-requests 64 --total-requests 256

  # 自定义token范围
  python benchmark_acc.py --input-tokens-min 500 --input-tokens-max 800 --output-tokens-min 200 --output-tokens-max 400

  # 自定义API端口和模型
  python benchmark_acc.py --port 8080 --model llama2
        """
    )

    # 并发和请求数配置
    parser.add_argument(
        '--concurrent-requests', '-c',
        type=int,
        default=128,
        help='并发请求数 (默认: 128)'
    )

    parser.add_argument(
        '--total-requests', '-t',
        type=int,
        default=128,
        help='总请求数 (默认: 128)'
    )

    # 输入token配置
    parser.add_argument(
        '--input-tokens-min',
        type=int,
        default=800,
        help='输入最小token数 (默认: 800)'
    )

    parser.add_argument(
        '--input-tokens-max',
        type=int,
        default=1000,
        help='输入最大token数 (默认: 1000)'
    )

    # 输出token配置
    parser.add_argument(
        '--output-tokens-min',
        type=int,
        default=300,
        help='输出最小token数 (默认: 300)'
    )

    parser.add_argument(
        '--output-tokens-max',
        type=int,
        default=500,
        help='输出最大token数 (默认: 500)'
    )

    # API配置
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=8063,
        help='API服务端口号 (默认: 8063)'
    )

    parser.add_argument(
        '--model', '-m',
        type=str,
        default="qwen",
        help='模型名称 (默认: qwen)'
    )

    return parser.parse_args()

# 生成随机长度的prompt模板
PROMPT_TEMPLATES = [
    "请详细分析以下数学问题并提供完整解答：",
    "请解释以下概念并给出具体例子：",
    "请分析以下情况并提供建议：",
    "请回答以下问题并详细说明原因：",
    "请描述以下场景并分析可能的结果：",
    "请解决以下问题并展示解题过程：",
    "请评估以下方案并提供改进建议：",
    "请分析以下数据并得出结论：",
]

# 话题库用于生成不同内容的prompt
TOPICS = [
    "机器学习算法优化",
    "数据结构与算法",
    "分布式系统设计",
    "深度学习网络架构",
    "云计算平台架构",
    "大数据处理技术",
    "人工智能应用场景",
    "软件工程最佳实践",
    "数据库性能优化",
    "网络安全防护策略",
    "移动应用开发",
    "前端框架比较",
    "后端服务架构",
    "微服务设计模式",
    "DevOps工具链",
]

# 用于填充的详细内容块 - 大幅扩展内容库
DETAIL_BLOCKS = [
    "请从多个角度进行分析，包括技术实现、性能考量、成本效益等方面。",
    "请考虑实际应用场景中可能遇到的挑战和解决方案。",
    "请提供具体的实施步骤和最佳实践建议。",
    "请分析当前市场趋势和未来发展方向。",
    "请比较不同解决方案的优缺点。",
    "请考虑scalability、维护性和可扩展性等因素。",
    "请提供相关的技术选型建议和架构设计考虑。",
    "请分析潜在风险和缓解策略。",
    "请详细说明实现原理和底层机制。",
    "请提供代码示例和具体实现方法。",
    "请分析性能瓶颈和优化策略。",
    "请考虑安全性和稳定性要求。",
    "请说明与其他系统的集成方案。",
    "请分析用户体验和交互设计。",
    "请提供测试策略和质量保证方法。",
    "请考虑维护成本和长期演进规划。",
    "请详细描述架构设计模式和最佳实践。包括微服务架构、分层架构、事件驱动架构等不同设计模式的适用场景和实现细节。",
    "请深入分析数据库设计和优化策略。包括索引优化、查询优化、分库分表、读写分离、缓存策略等方面的技术细节和实施方案。",
    "请全面评估云原生技术栈。包括容器化部署、Kubernetes编排、服务网格、监控告警、自动扩缩容等云原生核心技术的选型和实施。",
    "请深入探讨人工智能和机器学习在该领域的应用。包括算法选择、模型训练、特征工程、超参数调优、模型部署和在线推理等技术环节。",
    "请详细分析分布式系统的设计原理。包括CAP理论、一致性模型、分布式事务、负载均衡、容错机制、服务发现等分布式系统核心概念。",
    "请全面阐述DevOps和CI/CD流水线设计。包括版本控制、自动化测试、持续集成、持续部署、基础设施即代码、监控和日志管理等方面。",
    "请深入研究前端技术栈和用户体验优化。包括现代前端框架、状态管理、组件化设计、性能优化、响应式设计、无障碍访问等前端技术要点。",
    "请详细探讨后端服务架构和API设计。包括RESTful API、GraphQL、gRPC、API网关、限流熔断、服务治理、链路追踪等后端技术实践。",
    "请全面分析网络安全和数据保护策略。包括身份认证、访问控制、数据加密、安全审计、威胁检测、合规性要求等安全技术措施。",
    "请深入讨论大数据处理和实时分析技术。包括数据采集、存储架构、批处理、流处理、数据湖、数据仓库、OLAP分析等大数据技术栈。",
    "请详细说明移动应用开发和跨平台技术。包括原生开发、混合开发、跨平台框架、移动端架构、性能优化、用户体验设计等移动技术方案。",
    "请全面评估技术团队建设和项目管理。包括团队组织架构、技能培养、代码规范、文档管理、敏捷开发、项目里程碑等团队管理实践。",
    "请深入分析业务需求和技术实现的平衡。包括需求分析、技术选型、架构权衡、性能指标、成本控制、风险评估等业务技术融合要点。",
    "请详细探讨开源技术和商业解决方案的选择。包括开源软件评估、许可证管理、社区生态、技术支持、商业化考量等开源商业化决策因素。"
]

def estimate_tokens(text):
    """
    修正的token估算方法
    
    根据实际测试结果调整：
    - 之前高估了约30-40%，需要大幅降低估算值
    """
    chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
    english_chars = len([c for c in text if c.isalpha() and not ('\u4e00' <= c <= '\u9fff')])
    other_chars = len(text) - chinese_chars - english_chars
    
    # 根据实际测试结果调整估算参数
    estimated_tokens = (
        chinese_chars / 1.8 +      # 中文字符，大幅降低估算
        english_chars / 5.5 +      # 英文字符，大幅降低估算
        other_chars / 1.5          # 标点、空格等，降低估算
    )
    
    return int(estimated_tokens)

def generate_random_prompt(target_tokens):
    """
    基于token估算生成指定长度的随机prompt
    
    改进策略：
    1. 先生成基础prompt
    2. 逐步添加内容块直到接近目标token数
    3. 如果内容块用完仍未达到目标，重复使用和组合
    4. 允许5-10%的误差范围
    """
    template = random.choice(PROMPT_TEMPLATES)
    topic = random.choice(TOPICS)
    
    # 基础prompt
    base_prompt = f"{template} {topic}。"
    current_tokens = estimate_tokens(base_prompt)
    
    # 允许的误差范围
    target_min = target_tokens * 0.95  # 缩小误差范围到5%
    target_max = target_tokens * 1.05
    
    # 如果基础prompt已经超过目标，直接返回
    if current_tokens >= target_min:
        return base_prompt
    
    # 复制并打乱detail blocks
    available_blocks = DETAIL_BLOCKS.copy()
    random.shuffle(available_blocks)
    block_index = 0
    
    # 多轮添加内容直到达到目标
    max_attempts = 100  # 增加尝试次数
    attempts = 0
    
    while current_tokens < target_min and attempts < max_attempts:
        attempts += 1
        
        # 如果用完了所有块，重新开始并打乱
        if block_index >= len(available_blocks):
            available_blocks = DETAIL_BLOCKS.copy()
            random.shuffle(available_blocks)
            block_index = 0
        
        # 选择当前块
        block = available_blocks[block_index]
        block_index += 1
        
        # 测试添加完整块
        test_prompt = base_prompt + " " + block
        test_tokens = estimate_tokens(test_prompt)
        
        if test_tokens <= target_max:
            # 可以添加完整块
            base_prompt = test_prompt
            current_tokens = test_tokens
        else:
            # 尝试添加部分内容
            words = block.split()
            for word_count in [len(words)//2, len(words)//3, len(words)//4, len(words)//5]:
                if word_count > 0:
                    partial_block = " ".join(words[:word_count])
                    partial_prompt = base_prompt + " " + partial_block
                    partial_tokens = estimate_tokens(partial_prompt)
                    
                    if partial_tokens <= target_max and partial_tokens > current_tokens:
                        base_prompt = partial_prompt
                        current_tokens = partial_tokens
                        break
    
    return base_prompt

async def send_request(session, semaphore, request_id, config):
    """
    发送单个请求
    """
    async with semaphore:  # 获取并发许可
        # 生成随机输入和输出长度
        input_tokens = random.randint(config.input_tokens_min, config.input_tokens_max)
        output_tokens = random.randint(config.output_tokens_min, config.output_tokens_max)
        
        prompt = generate_random_prompt(input_tokens)
        actual_input_tokens = estimate_tokens(prompt)
        
        data = {
            "model": config.model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": output_tokens,
            "temperature": 0.7
        }
        
        start_time = time.time()
        try:
            async with session.post(config.url, json=data) as response:
                result = await response.json()
                end_time = time.time()
                
                latency = end_time - start_time
                
                # 提取响应信息
                if 'choices' in result and len(result['choices']) > 0:
                    response_content = result['choices'][0]['message']['content']
                    actual_output_tokens = estimate_tokens(response_content)
                    
                    # 尝试从服务器获取真实token数（如果有的话）
                    if 'usage' in result:
                        actual_input_tokens = result['usage'].get('prompt_tokens', actual_input_tokens)
                        actual_output_tokens = result['usage'].get('completion_tokens', actual_output_tokens)
                        
                else:
                    response_content = "Error in response"
                    actual_output_tokens = 0
                
                print(f"请求 {request_id:3d} 完成 - 延迟: {latency:.2f}s - 输入: {actual_input_tokens} tokens - 输出: {actual_output_tokens} tokens")
                
                return {
                    'request_id': request_id,
                    'latency': latency,
                    'input_tokens': actual_input_tokens,
                    'output_tokens': actual_output_tokens,
                    'success': True,
                    'response': response_content[:500] + "..." if len(response_content) > 500 else response_content
                }
                
        except Exception as e:
            end_time = time.time()
            latency = end_time - start_time
            print(f"请求 {request_id:3d} 失败 - 延迟: {latency:.2f}s - 错误: {str(e)}")
            return {
                'request_id': request_id,
                'latency': latency,
                'input_tokens': actual_input_tokens,
                'output_tokens': 0,
                'success': False,
                'error': str(e)
            }

async def main():
    # 解析命令行参数
    args = parse_arguments()

    # 创建配置对象
    try:
        config = BenchmarkConfig(args)
    except ValueError as e:
        print(f"配置错误: {e}", file=sys.stderr)
        sys.exit(1)
    
    print(f"开始基准测试:")
    print(f"- 并发数: {config.concurrent_requests}")
    print(f"- 总请求数: {config.total_requests}")
    print(f"- 输入token范围: {config.input_tokens_min}-{config.input_tokens_max}")
    print(f"- 输出token范围: {config.output_tokens_min}-{config.output_tokens_max}")
    print(f"- API端口: {config.port}")
    print(f"- 目标URL: {config.url}")
    print(f"- 模型: {config.model}")
    print("-" * 60)
    
    # 创建信号量控制并发数
    semaphore = asyncio.Semaphore(config.concurrent_requests)
    
    start_time = time.time()
    
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=300)) as session:
        # 创建所有请求任务
        tasks = [
            send_request(session, semaphore, i+1, config) 
            for i in range(config.total_requests)
        ]
        
        # 执行所有请求
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    successful_requests = [r for r in results if isinstance(r, dict) and r.get('success', False)]
    failed_requests = [r for r in results if isinstance(r, dict) and not r.get('success', False)]
    
    if successful_requests:
        avg_latency = sum(r['latency'] for r in successful_requests) / len(successful_requests)
        min_latency = min(r['latency'] for r in successful_requests)
        max_latency = max(r['latency'] for r in successful_requests)
        
        avg_input_tokens = sum(r['input_tokens'] for r in successful_requests) / len(successful_requests)
        avg_output_tokens = sum(r['output_tokens'] for r in successful_requests) / len(successful_requests)
    else:
        avg_latency = min_latency = max_latency = 0
        avg_input_tokens = avg_output_tokens = 0
    
    # 打印统计结果
    print("\n" + "=" * 60)
    print("基准测试结果:")
    print(f"总耗时: {total_time:.2f}s")
    print(f"成功请求: {len(successful_requests)}/{config.total_requests}")
    print(f"失败请求: {len(failed_requests)}")
    print(f"成功率: {len(successful_requests)/config.total_requests*100:.1f}%")
    print(f"平均QPS: {len(successful_requests)/total_time:.2f}")
    print(f"平均延迟: {avg_latency:.2f}s")
    print(f"最小延迟: {min_latency:.2f}s")
    print(f"最大延迟: {max_latency:.2f}s")
    print(f"平均输入tokens: {avg_input_tokens:.0f}")
    print(f"平均输出tokens: {avg_output_tokens:.0f}")
    
    # 打印一些响应内容示例
    if successful_requests:
        print("\n" + "=" * 60)
        print("响应内容示例:")
        print("-" * 60)
        
        # 显示前5个请求
        print("\n🚀 前5个请求 (测试开始时):")
        for i, req in enumerate(successful_requests[:5]):
            print(f"\n【前{i+1}】请求ID: {req['request_id']}")
            print(f"输入tokens: {req['input_tokens']}, 输出tokens: {req['output_tokens']}")
            print(f"响应内容:")
            response = req.get('response', 'No response content')
            # 显示完整响应内容，不截断
            if response.endswith('...'):
                # 如果是被截断的，显示原始响应
                print(response[:-3])  # 去掉'...'
                print("(内容已截断)")
            else:
                print(response)
            print("-" * 40)
        
        # 显示后5个请求
        if len(successful_requests) > 5:
            print("\n🏁 后5个请求 (测试结束时):")
            for i, req in enumerate(successful_requests[-5:]):
                print(f"\n【后{i+1}】请求ID: {req['request_id']}")
                print(f"输入tokens: {req['input_tokens']}, 输出tokens: {req['output_tokens']}")
                print(f"响应内容:")
                response = req.get('response', 'No response content')
                # 显示完整响应内容，不截断
                if response.endswith('...'):
                    # 如果是被截断的，显示原始响应
                    print(response[:-3])  # 去掉'...'
                    print("(内容已截断)")
                else:
                    print(response)
                print("-" * 40)
    
    if failed_requests:
        print("\n失败请求详情:")
        for req in failed_requests[:5]:  # 只显示前5个失败请求
            print(f"  请求 {req['request_id']}: {req.get('error', 'Unknown error')}")

if __name__ == "__main__":
    try:
        # 运行异步任务
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"运行错误: {e}", file=sys.stderr)
        sys.exit(1)
