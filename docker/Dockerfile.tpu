ARG NIGHTLY_DATE="20250124"
ARG BASE_IMAGE="us-central1-docker.pkg.dev/tpu-pytorch-releases/docker/xla:nightly_3.10_tpuvm_$NIGHTLY_DATE"

FROM $BASE_IMAGE
WORKDIR /workspace/vllm

# Install some basic utilities
RUN apt-get update && apt-get install -y \
    git \
    ffmpeg libsm6 libxext6 libgl1

# Build vLLM.
COPY . .
ARG GIT_REPO_CHECK=0
RUN --mount=type=bind,source=.git,target=.git \
    if [ "$GIT_REPO_CHECK" != 0 ]; then bash tools/check_repo.sh; fi

# Remove existing versions of dependencies
RUN pip uninstall -y torch torch_xla torchvision

ENV VLLM_TARGET_DEVICE="tpu"
RUN --mount=type=cache,target=/root/.cache/pip \
    --mount=type=bind,source=.git,target=.git \
    python3 -m pip install \
        -r requirements/tpu.txt
RUN python3 setup.py develop

# install development dependencies (for testing)
RUN python3 -m pip install -e tests/vllm_test_utils

CMD ["/bin/bash"]
