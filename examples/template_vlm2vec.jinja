{%- if messages | length > 1 -%}
    {{ raise_exception('Embedding models should only embed one message at a time') }}
{%- endif -%}

{% set vars = namespace(parts=[], next_image_id=1) %}
{%- for message in messages -%}
    {%- for content in message['content'] -%}
        {%- if content['type'] == 'text' -%}
            {%- set vars.parts = vars.parts + [content['text']] %}
        {%- elif content['type'] == 'image' -%}
            {%- set vars.parts = vars.parts + ['<|image_{i:d}|>'.format(i=vars.next_image_id)] %}
            {%- set vars.next_image_id = vars.next_image_id + 1 %}
        {%- endif -%}
    {%- endfor -%}
{%- endfor -%}
{{ vars.parts | join(' ') }}
